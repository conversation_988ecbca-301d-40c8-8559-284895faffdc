<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Projects</title>
    <link rel="stylesheet" href="../style.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
</head>
<body class="bg-dark-bg text-primary-text font-sans min-h-screen flex flex-col">
    <!-- Header -->
    <header class="flex justify-between items-center py-6 px-4 sm:px-6 lg:px-8 mb-10">
        <div class="flex items-center gap-2">
            <img src="/public/icons/Style=Default.svg" alt="Logo" class="w-5 h-5" />
            <span class="font-mono font-bold text-light-text text-xl">Amen</span>
        </div>
        <nav class="flex items-center gap-8 text-base font-mono lowercase">
            <a href="index.html" class="hover:text-accent-purple transition">#home</a>
            <a href="Projects.html" class="text-accent-purple font-bold relative after:content-[''] after:block after:h-0.5 after:bg-accent-purple after:scale-x-0 hover:after:scale-x-100 after:transition-transform after:duration-300 after:origin-left">#projects</a>
            <a href="About.html" class="hover:text-accent-purple transition">#about-me</a>
            <a href="contacts.html" class="hover:text-accent-purple transition">#contacts</a>
        </nav>
    </header>

    <main class="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 w-full">
        <section class="mb-12 reveal-on-scroll">
            <h1 class="text-3xl font-mono font-bold text-accent-purple mb-10">#projects</h1>

            <!-- PFA Project: SECoT -->
            <h2 class="text-2xl font-mono font-bold text-light-text mb-6">PFA Project: SECoT</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- PFA_SECOT -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">PFA_SECOT</h3>
                    <p class="text-secondary-text mb-4">IoT security audit system exploiting MQTT vulnerabilities.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/PFA_SECOT" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Web-IOT-Weather-Dashboard -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Web-IOT-Weather-Dashboard</h3>
                    <p class="text-secondary-text mb-4">React frontend + Python Flask backend using MQTT and Chart.js to display weather sensor data.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Web-IOT-Weather-Dashboard" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- SECoT Core Firmware -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">SECoT Core Firmware</h3>
                    <p class="text-secondary-text mb-4">Firmware for SECoT device acting like a Flipper Zero; supports attacks like ARP spoofing, deauth, evil twin, beacon flood, and combinations. (C++/Arduino)</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/secot-core-firmware" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- SECoT Victim Firmware -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">SECoT Victim Firmware</h3>
                    <p class="text-secondary-text mb-4">Firmware for IoT victim devices targeted by SECoT attacks; demonstrates wireless network vulnerabilities. (C++/Arduino)</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/secot-victim-firmware" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- SECoT CLI Tool -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">SECoT CLI Tool (Rust)</h3>
                    <p class="text-secondary-text mb-4">CLI tool in Rust interacting with SECoT ESP32 device; supports network scanning and attack simulation.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/secot-cli" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>
            </div>

            <!-- Python Projects -->
            <h2 class="text-2xl font-mono font-bold text-light-text mb-6">Python Projects (Up to discussion)</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- Email Spam Classifier -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Email Spam Classifier</h3>
                    <p class="text-secondary-text mb-4">Spam message classification using Python ML.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Spam_Classifier" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Basic Chatbot Interface -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Basic Chatbot Interface</h3>
                    <p class="text-secondary-text mb-4">Python Tkinter + PyTorch basic chatbot with rule-based responses.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Basic-Chatbot-Interface" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Simple Keylogger -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Simple Keylogger</h3>
                    <p class="text-secondary-text mb-4">Python keylogger for educational purposes, logs keystrokes to a file.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Simple-Keylogger" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Basic Cryptography & Hashing Scripts -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Basic Cryptography & Hashing Scripts</h3>
                    <p class="text-secondary-text mb-4">Python scripts illustrating security concepts: Caesar, RSA encryption, SHA hashing, OpenSSL manipulation.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Basic-Cryptography-Hashing" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>
            </div>

            <!-- Frontend -->
            <h2 class="text-2xl font-mono font-bold text-light-text mb-6">Frontend</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- Web-IOT-Weather-Dashboard duplicated on PFA, optional to keep here -->

                <!-- amazon-copy -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">amazon-copy</h3>
                    <p class="text-secondary-text mb-4">Amazon e-commerce frontend clone built with React.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/amazon-copy" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Online_Shop_Example -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Online_Shop_Example</h3>
                    <p class="text-secondary-text mb-4">Example online shop project demonstrating fullstack concepts.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Online_Shop_Example" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Introduction_-_React -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Introduction_-_React</h3>
                    <p class="text-secondary-text mb-4">Basic React tutorial project for learning React fundamentals.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Introduction_-_React" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Portfolio-with-Tailwind-CSS -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Portfolio-with-Tailwind-CSS</h3>
                    <p class="text-secondary-text mb-4">Personal portfolio site using Tailwind CSS.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Portfolio-with-Tailwind-CSS" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Quiz -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Quiz</h3>
                    <p class="text-secondary-text mb-4">Simple interactive quiz app.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Quiz" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Egg_Timer -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Egg_Timer</h3>
                    <p class="text-secondary-text mb-4">Timer app implemented in JavaScript.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Egg_Timer" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>
            </div>

            <!-- Backend & APIs -->
            <h2 class="text-2xl font-mono font-bold text-light-text mb-6">Backend & APIs</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- Express-Rest-API -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Express-Rest-API</h3>
                    <p class="text-secondary-text mb-4">RESTful API built with Express.js and Node.js.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Express-Rest-API" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Backend-Simple -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Backend-Simple</h3>
                    <p class="text-secondary-text mb-4">Basic backend project with Node.js.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Backend-Simple" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- MySQL-API -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">MySQL-API</h3>
                    <p class="text-secondary-text mb-4">API using MySQL database connectivity.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/MySQL-API" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Project-Template -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Project-Template</h3>
                    <p class="text-secondary-text mb-4">Boilerplate template project for starting backend development.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Project-Template" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>
            </div>

            <!-- AI & ML -->
            <h2 class="text-2xl font-mono font-bold text-light-text mb-6">AI & ML</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- Email Spam Classifier -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Email Spam Classifier</h3>
                    <p class="text-secondary-text mb-4">Spam message classification using Python ML.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Spam_Classifier" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Basic Chatbot Interface -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Basic Chatbot Interface</h3>
                    <p class="text-secondary-text mb-4">Python Tkinter + PyTorch basic chatbot with rule-based responses.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Basic-Chatbot-Interface" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>
            </div>

            <!-- Cybersecurity -->
            <h2 class="text-2xl font-mono font-bold text-light-text mb-6">🔒 Cybersecurity</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- PFA_SECOT -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">PFA_SECOT</h3>
                    <p class="text-secondary-text mb-4">IoT security audit system exploiting MQTT vulnerabilities.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/PFA_SECOT" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- SECoT CLI Tool (Rust) -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">SECoT CLI Tool (Rust)</h3>
                    <p class="text-secondary-text mb-4">CLI tool in Rust interacting with SECoT ESP32 device; supports network scanning and attack simulation.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/secot-cli" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Simple Keylogger -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Simple Keylogger</h3>
                    <p class="text-secondary-text mb-4">Python keylogger for educational purposes, logs keystrokes to a file.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Simple-Keylogger" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Basic Cryptography & Hashing Scripts -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Basic Cryptography & Hashing Scripts</h3>
                    <p class="text-secondary-text mb-4">Python scripts illustrating security concepts: Caesar, RSA encryption, SHA hashing, OpenSSL manipulation.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Basic-Cryptography-Hashing" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>
            </div>

            <!-- Embedded & IoT -->
            <h2 class="text-2xl font-mono font-bold text-light-text mb-6">🧮 Hardware & Embedded Systems</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- FPGA Face Detection -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Real-Time-Face-Detection-and-Recognition-Using-FPGA-and-CNN-Accelerator</h3>
                    <p class="text-secondary-text mb-4">FPGA-based face detection & recognition using CNN.</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/Real-Time-Face-Detection-and-Recognition-Using-FPGA-and-CNN-Accelerator" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- SECoT Core Firmware -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">SECoT Core Firmware</h3>
                    <p class="text-secondary-text mb-4">Firmware for SECoT device acting like a Flipper Zero; supports attacks like ARP spoofing, deauth, evil twin, beacon flood, and combinations. (C++/Arduino)</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/secot-core-firmware" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- SECoT Victim Firmware -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">SECoT Victim Firmware</h3>
                    <p class="text-secondary-text mb-4">Firmware for IoT victim devices targeted by SECoT attacks; demonstrates wireless network vulnerabilities. (C++/Arduino)</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/secot-victim-firmware" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Smart Light Firmware -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Smart Light Firmware</h3>
                    <p class="text-secondary-text mb-4">Firmware controlling smart lighting devices, enabling networked control and automation features. (C++/Arduino)</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/smart-light-firmware" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>

                <!-- Weather Station Firmware -->
                <div class="bg-card-bg border border-border-primary rounded-lg p-6 shadow-card-glow hover:border-accent-purple transition-all duration-300 flex flex-col hover:scale-105 hover:shadow-2xl reveal-on-scroll">
                    <h3 class="font-mono font-bold text-light-text text-xl mb-2">Weather Station Firmware</h3>
                    <p class="text-secondary-text mb-4">Firmware managing sensors and data acquisition in weather monitoring IoT devices; integrates with MQTT for real-time data streaming. (C++/Arduino)</p>
                    <div class="mt-auto flex gap-3">
                        <a href="https://github.com/Amen-ellah-kerimi/weather-station-firmware" target="_blank" rel="noopener" class="px-4 py-2 border border-accent-purple text-accent-purple font-mono rounded-md hover:bg-accent-purple hover:text-dark-bg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent-purple">GitHub</a>
                    </div>
                </div>
            </div>
        </section>
    </main>
</body>
</html>
