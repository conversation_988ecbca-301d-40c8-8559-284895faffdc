# Portfolio Personnel – <PERSON><PERSON>

## Présentation

Ce projet est un portfolio personnel moderne et responsive, développé dans le cadre d’un stage pour mettre en pratique Tailwind CSS, HTML, JavaScript et Vite. Il met en avant mes compétences, mes projets et mes informations de contact dans une interface élégante et accessible.

## Objectifs du Projet

- Maîtrise de Tailwind CSS et du responsive design
- Développement d’une application web complète et modulaire
- Application des bonnes pratiques d’accessibilité et d’UX
- Présentation claire de mon parcours, mes compétences et mes réalisations

## Stack Technique

- **HTML5** : Structure des pages
- **Tailwind CSS** : Stylisation utilitaire et responsive
- **JavaScript Vanilla** : Interactivité (animations, scroll, copier email, etc.)
- **Vite** : Environnement de développement et build rapide
- **PostCSS** : Traitement CSS

## Fonctionnalités Clés

- **Navigation moderne** avec header et footer cohérents
- **Section Hero** avec effet de texte animé (typing)
- **Présentation des compétences** sous forme de tags interactifs, groupés par catégorie
- **Grille de projets** responsive, chaque carte détaillant un projet (titre, description, stack, lien GitHub)
- **Effets d’animation** : fade-in au scroll, hover sur cartes et boutons
- **Scroll-to-top** : bouton flottant pour remonter rapidement
- **Copie d’email** : clic sur l’email pour copier dans le presse-papiers
- **Accessibilité** : focus rings, contrastes, navigation clavier

## Structure du Projet

```
portfolio/
│
├── public/
│   ├── icons/         # Icônes SVG et PNG (réseaux, UI, décorations)
│   └── vite.svg       # Logo Vite
│
├── pages/             # Pages HTML (Home, About, Projects, Contacts)
│
├── js/
│   └── main.js        # Script principal (animations, scroll, copier email...)
│
├── style.css          # Fichier CSS principal (inclut Tailwind)
├── tailwind.config.js # Config Tailwind CSS
├── postcss.config.js  # Config PostCSS
├── vite.config.js     # Config Vite
├── package.json       # Dépendances et scripts
├── package-lock.json  # Lockfile npm
├── .gitignore         # Fichiers/dossiers à ignorer
└── README.md          # Ce fichier
```

## Pages et Sections

- **Home** : Présentation, aspirations, aperçu des projets et compétences
- **About** : Parcours, compétences détaillées, technologies maîtrisées
- **Projects** : Grille de projets (IA, Full-Stack, Cybersécurité, IoT)
- **Contacts** : Email, LinkedIn, GitHub, liens directs

## Déploiement

Le projet peut être déployé sur Vercel, Netlify ou GitHub Pages. Build avec :

```bash
npm install
npm run build
```

## Auteur

Amen Ellah Kerimi  
Étudiant en Ingénierie des Systèmes Informatiques  
📧 <EMAIL>  
[LinkedIn](https://www.linkedin.com/in/amen-ellah-kerimi-354a85286)  
[GitHub](https://github.com/Amen-ellah-kerimi)

---

> Ce portfolio est une vitrine de mes compétences et de mon engagement dans le développement web moderne, avec un accent sur la qualité, la réactivité et l’accessibilité. 