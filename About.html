<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>About <PERSON><PERSON></title>
    <link rel="stylesheet" href="./style.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
</head>

<body class="bg-dark-bg text-primary-text font-sans min-h-screen">
    <!-- Header -->
    <header class="flex justify-between items-center py-6 px-4 sm:px-6 lg:px-8 mb-10">
        <div class="flex items-center gap-2">
            <!-- Placeholder icon (replace with your own if needed) -->
            <img src="/public/icons/Style=Default.svg" alt="Logo" class="w-5 h-5" />
            <span class="font-mono font-bold text-light-text text-xl">Amen</span>
        </div>
        <nav class="flex items-center gap-8 text-base font-mono lowercase">
            <a href="index.html" class="hover:text-accent-purple transition">#home</a>
            <a href="Projects.html" class="hover:text-accent-purple transition">#projects</a>
            <a href="About.html" class="text-accent-purple font-bold">#about-me</a>
            <a href="contacts.html" class="hover:text-accent-purple transition">#contacts</a>
        </nav>
    </header>
    <main class="max-w-7xl mx-auto px-6 sm:px-12 lg:px-24 py-16">
        <!-- About-me Hero Section -->
        <section
            class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center min-h-[50vh] relative mb-20 reveal-on-scroll">
            <!-- Left: About Text (now full width) -->
            <div class="z-10 flex flex-col justify-center max-w-2xl mx-auto lg:mx-0">
                <h2 class="text-3xl font-mono font-bold text-accent-purple mb-2">#about-me</h2>
                <p class="font-mono text-secondary-text mb-4">Who am I?</p>
                <p class="font-mono text-lg md:text-xl text-primary-text mb-6">Hi, I'm Amen Ellah Kerimi!<br><br>
                    I'm a 2nd-year Computer Systems Engineering student at the Faculty of Sciences of Bizerte (FSB). I'm
                    passionate about full-stack development, artificial intelligence, and always eager to learn new
                    things.<br><br>
                    I love transforming ideas into real projects and am constantly exploring new technologies. My
                    curiosity and motivation drive me to grow as a developer and problem solver.</p>
            </div>
        </section>

        <section class="mb-16">
            <h2 class="text-2xl font-bold text-accent-purple mb-6">Skills</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Left: Full-Stack, AI/ML, Web Frameworks -->
                <div>
                    <h3 class="text-xl font-semibold text-light-text mb-2">Languages</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span
                            class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Python</span>
                        <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">C</span>
                        <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">C++</span>
                        <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Java</span>
                        <span
                            class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">HTML/CSS/JS</span>
                        <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Rust</span>
                    </div>
                    <h3 class="text-xl font-semibold text-light-text mb-2">Frontend Frameworks & Libraries</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">React</span>
                        <span
                            class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">NEXT.JS</span>
                        <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">VITE</span>
                        <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Tailwind
                            CSS</span>
                        <span
                            class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">ChartJS</span>
                    </div>
                    <h3 class="text-xl font-semibold text-light-text mb-2">AI / Machine Learning</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">NumPy</span>
                        <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">Pandas</span>
                        <span
                            class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">Matplotlib</span>
                        <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">PyTorch
                            (learning)</span>
                        <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">Scikit-learn
                            (learning)</span>
                        <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">NLTK</span>
                    </div>
                </div>
                <!-- Right: Databases, Tools, Cybersecurity, IoT, Systems -->
                <div>
                    <h3 class="text-xl font-semibold text-light-text mb-2">Backend & Databases</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Flask</span>
                        <span
                            class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Express.JS</span>
                        <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Prisma
                            ORM</span>
                        <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Drizzle
                            ORM</span>
                        <span
                            class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">SQLite</span>
                        <span
                            class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">PostgreSQL</span>
                        <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">MySQL</span>
                    </div>
                    <h3 class="text-xl font-semibold text-light-text mb-2">Tools & Platforms</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">Git</span>
                        <span
                            class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">Docker</span>
                        <span
                            class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">Jupyter</span>
                    </div>
                    <h3 class="text-xl font-semibold text-light-text mb-2">Cybersecurity</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span
                            class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Wireshark</span>
                        <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Burp
                            Suite</span>
                        <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Nmap</span>
                    </div>
                    <h3 class="text-xl font-semibold text-light-text mb-2">Embedded & IoT</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span
                            class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Arduino</span>
                        <span
                            class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">ESP32/ESP8266</span>
                        <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">MQTT</span>
                    </div>
                    <h3 class="text-xl font-semibold text-light-text mb-2">Operating Systems</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span
                            class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">Windows</span>
                        <span
                            class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">Linux</span>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <!-- Footer -->
    <footer
        class="w-full flex flex-col md:flex-row justify-between items-center gap-4 px-4 sm:px-8 py-6 text-secondary-text font-mono text-sm border-t border-border-accent bg-dark-bg mt-8">
        <div class="flex flex-col items-center md:items-start">
            <div class="flex items-center gap-2 mb-1">
                <img src="/public/icons/Style=Default.svg" alt="Logo" class="w-4 h-4" />
                <span class="font-bold text-light-text">Amen Ellah</span>
            </div>
            <a href="mailto:<EMAIL>"
                class="copy-email hover:underline text-secondary-text"><EMAIL></a>
            <span class="text-xs text-accent-purple mt-1">Full-Stack & AI Enthusiast</span>
        </div>
        <div class="flex flex-col items-center md:items-end gap-2">
            <div class="flex gap-4 mb-1">
                <a href="https://github.com/Amen-ellah-kerimi" target="_blank" rel="noopener" title="GitHub"><img
                        src="/public/icons/Github.svg" class="w-5 h-5" alt="GitHub" /></a>
                <a href="https://www.linkedin.com/in/amen-ellah-kerimi-354a85286" target="_blank" rel="noopener"
                    title="LinkedIn"><img src="/public/icons/Linkedin.svg" class="w-5 h-5" alt="LinkedIn" /></a>
            </div>
            <span class="text-xs">&copy; Copyright 2025. Made by Amen Ellah.</span>
        </div>
    </footer>
    <script src="./js/main.js"></script>
</body>

</html>