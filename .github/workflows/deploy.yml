# .github/workflows/deploy.yml

name: Deploy to GitHub Pages

on:
  push:
    branches:
      - master # Keep 'master' if that's your branch, or 'main' if that's it

# Add these permissions:
permissions:
  contents: write # Needed to push the build artifacts
  pages: write    # Needed to deploy to GitHub Pages
  id-token: write # Needed for OIDC authentication by GitHub Pages

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20' # Confirm this matches your local Node.js version
          cache: 'npm'

      - name: Install dependencies
        run: npm install

      - name: Build project
        run: npm run build

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist/
          # If you have a multi-page app and want to use client-side routing (e.g., React Router)
          # without hash mode, you might need to copy index.html to 404.html
          # cp dist/index.html dist/404.html # Uncomment if needed for SPA routing
