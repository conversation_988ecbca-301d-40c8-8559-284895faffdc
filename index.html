<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/public/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Portfolio - Tailwind CSS</title>
    <link rel="stylesheet" href="../style.css" />
  </head>
  <body class="bg-dark-bg text-primary-text font-sans min-h-screen">
    <!-- Header -->
    <header class="flex justify-between items-center py-6 px-4 sm:px-6 lg:px-8 mb-10">
        <div class="flex items-center gap-2">
            <img src="/public/icons/Style=Default.svg" alt="Logo" class="w-5 h-5" />
            <span class="font-mono font-bold text-light-text text-xl">Amen</span>
        </div>
        <nav class="flex items-center gap-8 text-base font-mono lowercase">
            <a href="Home.html" class="text-accent-purple font-bold">#home</a>
            <a href="Projects.html" class="hover:text-accent-purple transition">#projects</a>
            <a href="About.html" class="hover:text-accent-purple transition">#about-me</a>
            <a href="contacts.html" class="hover:text-accent-purple transition">#contacts</a>
        </nav>
    </header>
    <main class="max-w-7xl mx-auto px-6 sm:px-12 lg:px-24 py-16">
      <!-- Hero Section -->
      <section class="flex items-center justify-center min-h-[60vh] relative mb-20 reveal-on-scroll">
        <div class="flex flex-col justify-center w-full max-w-5xl mx-auto">
          <h1 class="text-5xl md:text-6xl lg:text-7xl font-mono font-bold text-light-text mb-8 leading-tight typing-hero" data-text="Amen Ellah Kerimi is an aspiring full-stack developer and AI enthusiast">
            Amen Ellah Kerimi is an aspiring full-stack developer and AI enthusiast
          </h1>
          <p class="font-mono text-2xl md:text-3xl text-primary-text mb-8 max-w-3xl">2nd-year Computer Systems Engineering student at FSB. Passionate about building innovative solutions and always eager to learn.</p>
        </div>
      </section>

      <!-- Quote Section -->
      <section class="mb-12 reveal-on-scroll">
        <div class="bg-quote-bg border border-border-accent rounded-lg p-6 flex flex-col md:flex-row items-center gap-4 shadow-quote-shadow">
          <span class="font-mono text-2xl text-accent-purple">"</span>
          <p class="font-mono text-lg text-light-text flex-1">With great power comes great electricity bill.</p>
          <span class="font-mono text-secondary-text text-right">— Dr. Who</span>
        </div>
      </section>

      <!-- Projects Preview -->
      <section class="mb-12 reveal-on-scroll">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-2xl font-bold text-accent-purple font-mono">#projects</h2>
          <a href="Projects.html" class="font-mono text-accent-blue hover:underline">View all →</a>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Project 1 -->
          <div class="bg-card-bg border border-border-accent rounded-lg p-4 flex flex-col shadow-card-glow hover:scale-105 hover:shadow-2xl reveal-on-scroll">
            <h3 class="font-mono font-bold text-light-text mb-1">Email Spam Classifier</h3>
            <p class="text-secondary-text text-sm mb-2">AI model to classify emails as spam or legitimate using NLTK, TF-IDF, and logistic regression.</p>
            <div class="flex flex-wrap gap-2 mb-2">
              <span class="bg-dark-bg text-accent-pink px-2 py-0.5 rounded font-mono text-xs">Python</span>
              <span class="bg-dark-bg text-accent-pink px-2 py-0.5 rounded font-mono text-xs">Scikit-learn</span>
              <span class="bg-dark-bg text-accent-pink px-2 py-0.5 rounded font-mono text-xs">NLTK</span>
            </div>
            <div class="flex gap-2 mt-auto">
              <a href="#" class="font-mono text-accent-blue hover:underline text-xs">GitHub</a>
            </div>
          </div>
          <!-- Project 2 -->
          <div class="bg-card-bg border border-border-accent rounded-lg p-4 flex flex-col shadow-card-glow hover:scale-105 hover:shadow-2xl reveal-on-scroll">
            <h3 class="font-mono font-bold text-light-text mb-1">IoT Weather Dashboard</h3>
            <p class="text-secondary-text text-sm mb-2">Dashboard to visualize real-time sensor data with React, Tailwind, ChartJS, Flask, and MQTT.</p>
            <div class="flex flex-wrap gap-2 mb-2">
              <span class="bg-dark-bg text-accent-blue px-2 py-0.5 rounded font-mono text-xs">React</span>
              <span class="bg-dark-bg text-accent-blue px-2 py-0.5 rounded font-mono text-xs">Flask</span>
              <span class="bg-dark-bg text-accent-blue px-2 py-0.5 rounded font-mono text-xs">MQTT</span>
            </div>
            <div class="flex gap-2 mt-auto">
              <a href="#" class="font-mono text-accent-blue hover:underline text-xs">GitHub</a>
            </div>
          </div>
          <!-- Project 3 -->
          <div class="bg-card-bg border border-border-accent rounded-lg p-4 flex flex-col shadow-card-glow hover:scale-105 hover:shadow-2xl reveal-on-scroll">
            <h3 class="font-mono font-bold text-light-text mb-1">PFA - IoT Security Audit System</h3>
            <p class="text-secondary-text text-sm mb-2">Educational platform for IoT security, with ESP32-based audit tool and vulnerable IoT systems.</p>
            <div class="flex flex-wrap gap-2 mb-2">
              <span class="bg-dark-bg text-accent-purple px-2 py-0.5 rounded font-mono text-xs">ESP32</span>
              <span class="bg-dark-bg text-accent-purple px-2 py-0.5 rounded font-mono text-xs">React</span>
              <span class="bg-dark-bg text-accent-purple px-2 py-0.5 rounded font-mono text-xs">Flutter</span>
            </div>
            <div class="flex gap-2 mt-auto">
              <a href="#" class="font-mono text-accent-blue hover:underline text-xs">GitHub</a>
            </div>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section class="mb-16">
        <h2 class="text-2xl font-bold text-accent-purple mb-6">Skills</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Left: Full-Stack, AI/ML, Web Frameworks -->
          <div>
            <h3 class="text-xl font-semibold text-light-text mb-2">Languages</h3>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Python</span>
              <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">C</span>
              <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">C++</span>
              <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Java</span>
              <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">HTML/CSS/JS</span>
              <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Rust</span>
            </div>
            <h3 class="text-xl font-semibold text-light-text mb-2">Frontend Frameworks & Libraries</h3>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">React</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">NEXT.JS</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">VITE</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Tailwind CSS</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">ChartJS</span>
            </div>
            <h3 class="text-xl font-semibold text-light-text mb-2">AI / Machine Learning</h3>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">NumPy</span>
              <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">Pandas</span>
              <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">Matplotlib</span>
              <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">PyTorch (learning)</span>
              <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">Scikit-learn (learning)</span>
              <span class="bg-card-bg text-accent-pink px-3 py-1 rounded-full font-mono text-sm">NLTK</span>
            </div>
          </div>
          <!-- Right: Databases, Tools, Cybersecurity, IoT, Systems -->
          <div>
            <h3 class="text-xl font-semibold text-light-text mb-2">Backend & DB</h3>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Flask</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Express.JS</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Prisma ORM</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Drizzle ORM</span>
              <span class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">SQLite</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">PostgreSQL</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">MySQL</span>
            </div>
            <h3 class="text-xl font-semibold text-light-text mb-2">Tools & Platforms</h3>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">Git</span>
              <span class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">Docker</span>
              <span class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">Jupyter</span>
            </div>
            <h3 class="text-xl font-semibold text-light-text mb-2">Cybersecurity</h3>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Wireshark</span>
              <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Burp Suite</span>
              <span class="bg-card-bg text-accent-purple px-3 py-1 rounded-full font-mono text-sm">Nmap</span>
            </div>
            <h3 class="text-xl font-semibold text-light-text mb-2">Embedded & IoT</h3>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">Arduino</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">ESP32/ESP8266</span>
              <span class="bg-card-bg text-accent-blue px-3 py-1 rounded-full font-mono text-sm">MQTT</span>
            </div>
            <h3 class="text-xl font-semibold text-light-text mb-2">Operating Systems</h3>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">Windows</span>
              <span class="bg-card-bg text-secondary-text px-3 py-1 rounded-full font-mono text-sm">Linux</span>
            </div>
          </div>
        </div>
      </section>

      <!-- About Me Preview -->
      <section class="mb-16 flex flex-col md:flex-row items-center gap-8">
        <div class="flex-1">
          <h2 class="text-2xl font-bold text-accent-purple font-mono mb-2">#about-me</h2>
          <p class="text-primary-text mb-4">Hi, I'm Amen Ellah Kerimi, a 2nd-year Computer Systems Engineering student at the Faculty of Sciences of Bizerte (FSB). I'm passionate about Full-Stack development, AI, cybersecurity, and IoT. I'm curious, motivated, and always learning. Currently seeking a summer internship to strengthen my practical skills and contribute to innovative projects.</p>
          <a href="About.html" class="inline-block px-5 py-2 border border-accent-purple text-accent-purple font-mono rounded hover:bg-card-bg transition">Read more →</a>
        </div>

      </section>

      <!-- Contacts Section -->
      <section class="mb-16">
        <h2 class="text-2xl font-bold text-accent-purple font-mono mb-4">#contacts</h2>
        <div class="bg-card-bg border border-border-accent rounded-lg p-6 flex flex-col md:flex-row items-center gap-6">
          <div class="flex-1">
            <p class="font-mono text-primary-text mb-2">Interested in freelance opportunities, teamwork, or just want to connect? Reach out via:</p>
            <div class="flex gap-4 items-center mb-2">
              <a href="mailto:<EMAIL>" title="Email"><img src="/public/icons/Email.svg" class="w-6 h-6" alt="Email" /></a>
              <a href="https://www.linkedin.com/in/amen-ellah-kerimi-354a85286" title="LinkedIn"><img src="/public/icons/Linkedin.svg" class="w-6 h-6" alt="LinkedIn" /></a>
              <a href="https://github.com/Amen-ellah-kerimi" title="GitHub"><img src="/public/icons/Github.svg" class="w-6 h-6" alt="GitHub" /></a>
            </div>
            <a href="mailto:<EMAIL>" class="copy-email hover:underline text-secondary-text"><EMAIL></a>
          </div>
          <div class="flex-1 flex flex-col items-center">
            <span class="font-mono text-light-text mb-2">Message me here</span>
            <a href="mailto:<EMAIL>" class="px-5 py-2 border border-accent-purple text-accent-purple font-mono rounded hover:bg-card-bg transition">Say Hello!</a>
          </div>
        </div>
      </section>
    </main>
    <!-- Footer -->
    <footer class="w-full flex flex-col md:flex-row justify-between items-center gap-4 px-4 sm:px-8 py-6 text-secondary-text font-mono text-sm border-t border-border-accent bg-dark-bg mt-8">
        <div class="flex flex-col items-center md:items-start">
            <div class="flex items-center gap-2 mb-1">
                <img src="/public/icons/Style=Default.svg" alt="Logo" class="w-4 h-4" />
                <span class="font-bold text-light-text">Amen Ellah</span>
            </div>
            <a href="mailto:<EMAIL>" class="copy-email hover:underline text-secondary-text"><EMAIL></a>
            <span class="text-xs text-accent-purple mt-1">Full-Stack & AI Enthusiast</span>
        </div>
        <div class="flex flex-col items-center md:items-end gap-2">
            <div class="flex gap-4 mb-1">
                <a href="https://github.com/Amen-ellah-kerimi" target="_blank" rel="noopener" title="GitHub"><img src="/public/icons/Github.svg" class="w-5 h-5" alt="GitHub" /></a>
                <a href="https://www.linkedin.com/in/amen-ellah-kerimi-354a85286" target="_blank" rel="noopener" title="LinkedIn"><img src="/public/icons/Linkedin.svg" class="w-5 h-5" alt="LinkedIn" /></a>
            </div>
            <span class="text-xs">&copy; Copyright 2025. Made by Amen Ellah.</span>
        </div>
    </footer>
    <script src="../js/main.js"></script>
  </body>
</html>
